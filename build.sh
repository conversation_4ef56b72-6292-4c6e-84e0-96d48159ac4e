#!/bin/bash

# Load environment variables from .env.production file
if [ -f .env.production ]; then
  export $(grep -v '^#' .env.production | xargs)
fi

docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --build-arg VITE_CORE_SERVICE_URL=https://backend-core-api.agentq.id \
  --build-arg VITE_BACKEND_URL=https://backend-app.agentq.id \
  --build-arg VITE_AI_SERVICE_URL=https://backend-ai-api.agentq.id \
  --build-arg VITE_WEBSOCKET_URL=wss://websocket-ai-single-test.agentq.id \
  --build-arg VITE_WEBSOCKET_TESTRUN_URL=wss://websocket-ai-test-run.agentq.id \
  --build-arg VITE_WEBSOCKET_SECURITY_URL=wss://websocket-ai-dast-single-test.agentq.id \
  --build-arg VITE_WEBSOCKET_TESTRUN_SECURITY_URL=wss://websocket-ai-dast-test-run.agentq.id \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_frontend_agentq:2.2.0 \
  --push .



